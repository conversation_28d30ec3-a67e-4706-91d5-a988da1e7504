#!/usr/bin/env python3
"""
下载大型Qwen3模型的脚本
包括Qwen3-Coder-480B-A35B-Instruct和Qwen3-235B-A22B Q4_K_M量化版本
"""

import os
import sys
from modelscope import snapshot_download
from huggingface_hub import snapshot_download as hf_download

def download_qwen3_coder_480b():
    """
    下载Qwen3-Coder-480B-A35B-Instruct模型
    """
    model_id = "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    cache_dir = "/mnt/2T-mac/models"
    
    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)
    
    print(f"🚀 开始下载模型: {model_id}")
    print(f"📁 下载目录: {cache_dir}")
    print("⚠️  注意：此模型非常大（~960GB），下载可能需要很长时间...")
    print("💡 建议确保有足够的存储空间和稳定的网络连接")
    
    try:
        # 先尝试ModelScope
        print("\n🔄 尝试从ModelScope下载...")
        model_dir = snapshot_download(
            model_id=model_id,
            cache_dir=cache_dir,
            revision='master'
        )
        
        print(f"✅ 模型下载完成！")
        print(f"📍 模型路径: {model_dir}")
        return model_dir
        
    except Exception as e:
        print(f"❌ ModelScope下载失败: {str(e)}")
        
        # 尝试HuggingFace
        try:
            print("\n🔄 尝试从HuggingFace下载...")
            model_dir = hf_download(
                repo_id=model_id,
                cache_dir=cache_dir,
                resume_download=True
            )
            
            print(f"✅ 模型下载完成！")
            print(f"📍 模型路径: {model_dir}")
            return model_dir
            
        except Exception as e2:
            print(f"❌ HuggingFace下载也失败: {str(e2)}")
            return None

def download_qwen3_235b_q4km():
    """
    下载Qwen3-235B-A22B Q4_K_M量化版本
    """
    # Q4_K_M量化版本通常在GGUF格式的仓库中
    model_id = "bartowski/Qwen3-235B-A22B-GGUF"  # 或其他GGUF仓库
    cache_dir = "/mnt/2T-mac/models"
    
    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)
    
    print(f"🚀 开始下载模型: {model_id}")
    print(f"📁 下载目录: {cache_dir}")
    print("⚠️  注意：Q4_K_M量化版本约140GB，下载需要较长时间...")
    
    try:
        # 使用HuggingFace下载GGUF格式
        print("\n🔄 从HuggingFace下载GGUF格式...")
        model_dir = hf_download(
            repo_id=model_id,
            cache_dir=cache_dir,
            resume_download=True,
            allow_patterns=["*Q4_K_M*", "*.json", "*.md"]  # 只下载Q4_K_M相关文件
        )
        
        print(f"✅ 模型下载完成！")
        print(f"📍 模型路径: {model_dir}")
        return model_dir
        
    except Exception as e:
        print(f"❌ 下载失败: {str(e)}")
        
        # 尝试其他GGUF仓库
        alternative_repos = [
            "ubergarm/Qwen3-235B-A22B-GGUF",
            "mradermacher/Qwen3-235B-A22B-GGUF",
            "QuantFactory/Qwen3-235B-A22B-GGUF"
        ]
        
        for repo in alternative_repos:
            try:
                print(f"\n🔄 尝试备用仓库: {repo}")
                model_dir = hf_download(
                    repo_id=repo,
                    cache_dir=cache_dir,
                    resume_download=True,
                    allow_patterns=["*Q4_K_M*", "*.json", "*.md"]
                )
                
                print(f"✅ 从备用仓库下载完成！")
                print(f"📍 模型路径: {model_dir}")
                return model_dir
                
            except Exception as e2:
                print(f"❌ 备用仓库 {repo} 也失败: {str(e2)}")
                continue
        
        return None

def check_disk_space():
    """
    检查磁盘空间
    """
    import shutil
    
    target_path = "/mnt/2T-mac"
    if os.path.exists(target_path):
        total, used, free = shutil.disk_usage(target_path)
        
        print(f"💾 磁盘空间检查 ({target_path}):")
        print(f"   总容量: {total // (1024**3):.1f} GB")
        print(f"   已使用: {used // (1024**3):.1f} GB")
        print(f"   可用空间: {free // (1024**3):.1f} GB")
        
        # 估算需要的空间
        required_space = 960 + 140  # 480B模型 + 235B Q4_K_M
        
        if free // (1024**3) < required_space:
            print(f"⚠️  警告: 可用空间可能不足！")
            print(f"   预计需要: ~{required_space} GB")
            print(f"   建议释放更多空间或选择其他存储位置")
            return False
        else:
            print(f"✅ 磁盘空间充足")
            return True
    else:
        print(f"❌ 目标路径不存在: {target_path}")
        return False

def main():
    """
    主函数
    """
    print("=" * 80)
    print("🎯 大型Qwen3模型下载工具")
    print("=" * 80)
    
    print("\n📋 下载计划:")
    print("1. Qwen3-Coder-480B-A35B-Instruct (~960GB)")
    print("2. Qwen3-235B-A22B Q4_K_M量化版本 (~140GB)")
    print("总计约: ~1.1TB")
    
    # 检查磁盘空间
    print("\n" + "=" * 80)
    if not check_disk_space():
        print("\n❌ 磁盘空间检查失败，请解决后重试")
        return
    
    # 询问用户确认
    print("\n" + "=" * 80)
    choice = input("🤔 确认开始下载吗？这将需要很长时间... (y/N): ").strip().lower()
    
    if choice not in ['y', 'yes']:
        print("❌ 用户取消下载")
        return
    
    print("\n🚀 开始下载流程...")
    
    # 下载第一个模型
    print("\n" + "=" * 80)
    print("📥 第1步: 下载Qwen3-Coder-480B-A35B-Instruct")
    print("=" * 80)
    
    coder_path = download_qwen3_coder_480b()
    
    if coder_path:
        print(f"✅ Qwen3-Coder-480B下载成功: {coder_path}")
    else:
        print("❌ Qwen3-Coder-480B下载失败")
    
    # 下载第二个模型
    print("\n" + "=" * 80)
    print("📥 第2步: 下载Qwen3-235B-A22B Q4_K_M")
    print("=" * 80)
    
    q4km_path = download_qwen3_235b_q4km()
    
    if q4km_path:
        print(f"✅ Qwen3-235B Q4_K_M下载成功: {q4km_path}")
    else:
        print("❌ Qwen3-235B Q4_K_M下载失败")
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 下载结果总结:")
    print("=" * 80)
    
    if coder_path:
        print(f"✅ Qwen3-Coder-480B: {coder_path}")
    else:
        print("❌ Qwen3-Coder-480B: 下载失败")
    
    if q4km_path:
        print(f"✅ Qwen3-235B Q4_K_M: {q4km_path}")
    else:
        print("❌ Qwen3-235B Q4_K_M: 下载失败")
    
    if coder_path and q4km_path:
        print("\n🎉 所有模型下载完成！")
        print("\n💡 使用建议:")
        print("- Qwen3-Coder-480B: 适合复杂编程任务")
        print("- Qwen3-235B Q4_K_M: 适合通用推理，内存需求较低")
        print("- 建议根据具体任务选择合适的模型")
    else:
        print("\n⚠️  部分模型下载失败，请检查网络连接和存储空间")

if __name__ == "__main__":
    main()
