#!/usr/bin/env python3
"""
测试Qwen3-14B模型加载和基本功能
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

def test_qwen3_14b():
    """
    测试Qwen3-14B模型的基本功能
    """
    model_path = "/mnt/2T-mac/models/Qwen/Qwen3-14B"
    
    print("🚀 开始测试Qwen3-14B模型...")
    print(f"模型路径: {model_path}")
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print("❌ 模型路径不存在！")
        return False
    
    try:
        print("\n📥 加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        print("✅ Tokenizer加载成功")
        
        print("\n📥 加载模型...")
        # 使用较低精度以节省内存
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,  # 使用半精度
            device_map="auto",          # 自动分配设备
            trust_remote_code=True,
            low_cpu_mem_usage=True      # 降低CPU内存使用
        )
        print("✅ 模型加载成功")
        
        # 测试基本对话
        print("\n🧪 测试基本对话功能...")
        test_prompt = "你好，请简单介绍一下你自己。"
        
        messages = [
            {"role": "user", "content": test_prompt}
        ]
        
        # 应用聊天模板
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=False  # 关闭思考模式进行快速测试
        )
        
        # 编码输入
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)
        
        print(f"输入: {test_prompt}")
        print("生成回复中...")
        
        # 生成回复
        with torch.no_grad():
            generated_ids = model.generate(
                **model_inputs,
                max_new_tokens=200,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # 解码输出
        output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()
        response = tokenizer.decode(output_ids, skip_special_tokens=True)
        
        print(f"回复: {response}")
        print("\n✅ 基本对话测试成功！")
        
        # 测试思考模式
        print("\n🧠 测试思考模式...")
        thinking_prompt = "计算 15 × 23 = ?"
        
        messages = [
            {"role": "user", "content": thinking_prompt}
        ]
        
        # 启用思考模式
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=True  # 启用思考模式
        )
        
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)
        
        print(f"输入: {thinking_prompt}")
        print("思考中...")
        
        with torch.no_grad():
            generated_ids = model.generate(
                **model_inputs,
                max_new_tokens=500,
                temperature=0.3,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()
        
        # 解析思考内容和最终答案
        try:
            # 查找思考结束标记
            index = len(output_ids) - output_ids[::-1].index(151668)  # </think>
        except ValueError:
            index = 0
        
        thinking_content = tokenizer.decode(output_ids[:index], skip_special_tokens=True).strip()
        final_answer = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip()
        
        print(f"思考过程: {thinking_content}")
        print(f"最终答案: {final_answer}")
        print("\n✅ 思考模式测试成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def check_system_info():
    """
    检查系统信息
    """
    print("🔍 系统信息检查:")
    print(f"Python版本: {torch.__version__}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 Qwen3-14B 模型测试")
    print("=" * 60)
    
    check_system_info()
    print("\n" + "=" * 60)
    
    success = test_qwen3_14b()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！Qwen3-14B模型工作正常。")
        print("\n💡 使用建议:")
        print("- 模型大小: 28GB")
        print("- 推荐GPU内存: 16GB+")
        print("- 支持混合思考模式")
        print("- 适合OpenRA等复杂策略游戏")
    else:
        print("❌ 测试失败，请检查模型文件和系统配置。")
    print("=" * 60)
