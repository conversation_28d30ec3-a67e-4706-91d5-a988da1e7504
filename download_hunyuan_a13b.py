#!/usr/bin/env python3
"""
从国内镜像下载Hunyuan-A13B模型
支持ModelScope和HF-Mirror镜像
"""

import os
import sys
import shutil
from modelscope import snapshot_download

def check_disk_space():
    """
    检查磁盘空间
    """
    target_path = "/mnt/2T-mac"
    if os.path.exists(target_path):
        total, used, free = shutil.disk_usage(target_path)

        print(f"💾 磁盘空间检查 ({target_path}):")
        print(f"   总容量: {total // (1024**3):.1f} GB")
        print(f"   已使用: {used // (1024**3):.1f} GB")
        print(f"   可用空间: {free // (1024**3):.1f} GB")

        # Hunyuan-A13B大约需要26GB
        required_space = 30  # 留一些余量

        if free // (1024**3) < required_space:
            print(f"⚠️  警告: 可用空间可能不足！")
            print(f"   预计需要: ~{required_space} GB")
            return False
        else:
            print(f"✅ 磁盘空间充足")
            return True
    else:
        print(f"❌ 目标路径不存在: {target_path}")
        return False

def download_from_modelscope():
    """
    从ModelScope下载Hunyuan-A13B
    """
    model_id = "Tencent-Hunyuan/Hunyuan-A13B-Instruct"
    cache_dir = "/mnt/2T-mac/models"

    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)

    print(f"🚀 从ModelScope下载: {model_id}")
    print(f"📁 下载目录: {cache_dir}")
    print("📊 预计大小: ~26GB")

    try:
        model_dir = snapshot_download(
            model_id=model_id,
            cache_dir=cache_dir,
            revision='master'
        )

        print(f"✅ ModelScope下载完成！")
        print(f"📍 模型路径: {model_dir}")
        return model_dir

    except Exception as e:
        print(f"❌ ModelScope下载失败: {str(e)}")
        return None

def download_from_hf_mirror():
    """
    从HF-Mirror下载Hunyuan-A13B
    """
    # 设置HF-Mirror环境变量
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

    from huggingface_hub import snapshot_download as hf_download

    model_id = "tencent/Hunyuan-A13B-Instruct"
    cache_dir = "/mnt/2T-mac/models"

    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)

    print(f"🚀 从HF-Mirror下载: {model_id}")
    print(f"🌐 镜像地址: https://hf-mirror.com")
    print(f"📁 下载目录: {cache_dir}")
    print("📊 预计大小: ~26GB")

    try:
        model_dir = hf_download(
            repo_id=model_id,
            cache_dir=cache_dir,
            resume_download=True
        )

        print(f"✅ HF-Mirror下载完成！")
        print(f"📍 模型路径: {model_dir}")
        return model_dir

    except Exception as e:
        print(f"❌ HF-Mirror下载失败: {str(e)}")
        return None

def download_hunyuan_a13b():
    """
    下载Hunyuan-A13B模型，优先使用国内镜像
    """
    print("🎯 开始下载Hunyuan-A13B模型")
    print("🇨🇳 优先使用国内镜像源")

    # 方法1: 尝试ModelScope
    print("\n" + "="*60)
    print("📥 方法1: 尝试ModelScope (推荐)")
    print("="*60)

    model_dir = download_from_modelscope()
    if model_dir:
        return model_dir

    # 方法2: 尝试HF-Mirror
    print("\n" + "="*60)
    print("📥 方法2: 尝试HF-Mirror")
    print("="*60)

    model_dir = download_from_hf_mirror()
    if model_dir:
        return model_dir

    print("\n❌ 所有国内镜像都下载失败")
    return None

def list_downloaded_files(model_dir):
    """
    列出下载的文件
    """
    if not model_dir or not os.path.exists(model_dir):
        return

    print(f"\n📋 下载的文件列表:")
    total_size = 0

    for root, dirs, files in os.walk(model_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                total_size += file_size
                print(f"  📄 {file} ({file_size / (1024**3):.2f} GB)")

    print(f"\n📊 总大小: {total_size / (1024**3):.2f} GB")

def main():
    """
    主函数
    """
    print("=" * 80)
    print("🎯 Hunyuan-A13B 国内镜像下载工具")
    print("=" * 80)

    print("\n📋 模型信息:")
    print("• 模型: Hunyuan-A13B-Instruct")
    print("• 开发者: 腾讯ARC实验室")
    print("• 参数量: 13B")
    print("• 大小: ~26GB")
    print("• 特点: 中英文双语，指令微调")

    print("\n🇨🇳 国内镜像源:")
    print("• ModelScope: 阿里云提供，速度快")
    print("• HF-Mirror: HuggingFace国内镜像")

    # 检查磁盘空间
    print("\n" + "=" * 80)
    if not check_disk_space():
        print("\n❌ 磁盘空间不足，请释放空间后重试")
        return

    print("\n" + "=" * 80)
    print("🚀 开始下载...")

    model_path = download_hunyuan_a13b()

    print("\n" + "=" * 80)
    print("📊 下载结果:")
    print("=" * 80)

    if model_path:
        print(f"✅ Hunyuan-A13B下载成功！")
        print(f"📍 模型路径: {model_path}")

        # 列出文件
        list_downloaded_files(model_path)

        print(f"\n💡 使用建议:")
        print(f"• 推荐内存: 16GB+ RAM")
        print(f"• 推荐显存: 8GB+ VRAM")
        print(f"• 支持框架: Transformers, vLLM, Ollama")
        print(f"• 适用场景: 中英文对话、代码生成、文本创作")

        print(f"\n🔧 快速测试:")
        print(f"```python")
        print(f"from transformers import AutoModelForCausalLM, AutoTokenizer")
        print(f"")
        print(f"model_path = '{model_path}'")
        print(f"tokenizer = AutoTokenizer.from_pretrained(model_path)")
        print(f"model = AutoModelForCausalLM.from_pretrained(model_path)")
        print(f"```")

    else:
        print(f"❌ 下载失败")
        print(f"💡 可能的解决方案:")
        print(f"• 检查网络连接")
        print(f"• 尝试使用VPN")
        print(f"• 手动从ModelScope网站下载")
        print(f"• 使用git clone命令")

        print(f"\n🔗 手动下载链接:")
        print(f"• ModelScope: https://modelscope.cn/models/TencentARC/Hunyuan-A13B-Instruct")
        print(f"• HuggingFace: https://huggingface.co/TencentARC/Hunyuan-A13B-Instruct")

if __name__ == "__main__":
    main()
