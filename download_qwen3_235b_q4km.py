#!/usr/bin/env python3
"""
下载Qwen3-235B-A22B Q4_K_M量化版本
"""

import os
import sys
from huggingface_hub import snapshot_download, hf_hub_download
import shutil

def check_disk_space():
    """
    检查磁盘空间
    """
    target_path = "/mnt/2T-mac"
    if os.path.exists(target_path):
        total, used, free = shutil.disk_usage(target_path)

        print(f"💾 磁盘空间检查 ({target_path}):")
        print(f"   总容量: {total // (1024**3):.1f} GB")
        print(f"   已使用: {used // (1024**3):.1f} GB")
        print(f"   可用空间: {free // (1024**3):.1f} GB")

        # Q4_K_M版本大约需要140GB
        required_space = 150  # 留一些余量

        if free // (1024**3) < required_space:
            print(f"⚠️  警告: 可用空间可能不足！")
            print(f"   预计需要: ~{required_space} GB")
            return False
        else:
            print(f"✅ 磁盘空间充足")
            return True
    else:
        print(f"❌ 目标路径不存在: {target_path}")
        return False

def download_qwen3_235b_q4km():
    """
    下载Qwen3-235B-A22B Q4_K_M量化版本
    """
    cache_dir = "/mnt/2T-mac/models"

    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)

    print(f"🚀 开始下载Qwen3-235B-A22B Q4_K_M量化版本")
    print(f"📁 下载目录: {cache_dir}")
    print("📊 预计大小: ~140GB")
    print("⏱️  预计时间: 根据网速而定，可能需要数小时")

    # 尝试多个GGUF仓库
    repos_to_try = [
        "Qwen/Qwen3-235B-A22B-GGUF",  # 官方GGUF仓库
        "unsloth/Qwen3-235B-A22B-GGUF",  # Unsloth版本
        "huihui-ai/Huihui-Qwen3-235B-A22B-abliterated-GGUF"  # 备用版本
    ]

    for i, repo_id in enumerate(repos_to_try, 1):
        print(f"\n🔄 尝试仓库 {i}/{len(repos_to_try)}: {repo_id}")

        try:
            # 先检查仓库是否存在以及有哪些文件
            from huggingface_hub import list_repo_files

            try:
                files = list_repo_files(repo_id)
                q4km_files = [f for f in files if 'Q4_K_M' in f and f.endswith('.gguf')]

                if not q4km_files:
                    print(f"❌ 仓库 {repo_id} 中没有找到Q4_K_M文件")
                    continue

                print(f"✅ 找到Q4_K_M文件: {q4km_files}")

            except Exception as e:
                print(f"❌ 无法访问仓库 {repo_id}: {str(e)}")
                continue

            # 下载模型
            print(f"📥 开始从 {repo_id} 下载...")

            model_dir = snapshot_download(
                repo_id=repo_id,
                cache_dir=cache_dir,
                resume_download=True,
                allow_patterns=["*Q4_K_M*", "*.json", "*.md", "README*"]
            )

            print(f"✅ 模型下载完成！")
            print(f"📍 模型路径: {model_dir}")

            # 列出下载的文件
            print(f"\n📋 下载的文件:")
            for root, dirs, files in os.walk(model_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"  📄 {file} ({file_size / (1024**3):.2f} GB)")

            return model_dir

        except Exception as e:
            print(f"❌ 从 {repo_id} 下载失败: {str(e)}")
            continue

    print(f"❌ 所有仓库都下载失败")
    return None

def main():
    """
    主函数
    """
    print("=" * 80)
    print("🎯 Qwen3-235B-A22B Q4_K_M 下载工具")
    print("=" * 80)

    print("\n📋 下载信息:")
    print("• 模型: Qwen3-235B-A22B")
    print("• 量化: Q4_K_M (4-bit量化)")
    print("• 大小: ~140GB")
    print("• 格式: GGUF")
    print("• 内存需求: 128GB+ RAM推荐")

    # 检查磁盘空间
    print("\n" + "=" * 80)
    if not check_disk_space():
        print("\n❌ 磁盘空间不足，请释放空间后重试")
        return

    print("\n" + "=" * 80)
    print("🚀 开始下载...")

    model_path = download_qwen3_235b_q4km()

    print("\n" + "=" * 80)
    print("📊 下载结果:")
    print("=" * 80)

    if model_path:
        print(f"✅ Qwen3-235B Q4_K_M下载成功！")
        print(f"📍 模型路径: {model_path}")

        print(f"\n💡 使用建议:")
        print(f"• 推荐内存: 128GB+ RAM")
        print(f"• 推理工具: llama.cpp, Ollama, 或 text-generation-webui")
        print(f"• 量化精度: Q4_K_M平衡了质量和大小")
        print(f"• 适用场景: 复杂推理、代码生成、多语言任务")

        print(f"\n🔧 快速测试命令:")
        print(f"# 使用llama.cpp测试:")
        print(f"# ./main -m {model_path}/*.gguf -p '你好，请介绍一下你自己。' -n 100")

    else:
        print(f"❌ 下载失败")
        print(f"💡 可能的解决方案:")
        print(f"• 检查网络连接")
        print(f"• 尝试使用VPN")
        print(f"• 手动从HuggingFace网站下载")
        print(f"• 使用git lfs clone命令")

if __name__ == "__main__":
    main()
