#!/usr/bin/env python3
"""
下载Qwen3 30B MoE模型的脚本
使用ModelScope平台下载模型文件
"""

import os
from modelscope import snapshot_download

def download_qwen3_moe():
    """
    下载Qwen3 30B MoE模型
    """
    # 模型ID - Qwen3 30B MoE在ModelScope上的标识符
    model_id = "Qwen/Qwen2.5-32B-Instruct"  # 使用最新的Qwen2.5-32B-Instruct模型

    # 设置下载目录
    cache_dir = "/mnt/2T-mac/models"

    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)

    print(f"开始下载模型: {model_id}")
    print(f"下载目录: {cache_dir}")
    print("注意：模型文件较大，下载可能需要较长时间...")

    try:
        # 下载模型
        model_dir = snapshot_download(
            model_id=model_id,
            cache_dir=cache_dir,
            revision='master'
        )

        print(f"模型下载完成！")
        print(f"模型路径: {model_dir}")

        # 列出下载的文件
        print("\n下载的文件列表:")
        for root, dirs, files in os.walk(model_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"  {file} ({file_size / (1024*1024):.2f} MB)")

    except Exception as e:
        print(f"下载失败: {str(e)}")
        print("请检查网络连接和ModelScope访问权限")

if __name__ == "__main__":
    download_qwen3_moe()
